use std::collections::HashMap;
use std::sync::RwLock;

use once_cell::sync::Lazy;

#[derive(Debug, Clone)]
pub struct Ticket {
    pub id: u64,
    pub user_id: u64,
    pub guild_id: u64,
    pub category_id: u64,
    pub channel_id: u64,
    pub contact_name: String,
    pub contact_identifier: String,
}

pub struct Tickets {
    tickets: RwLock<HashMap<u64, Ticket>>,
}

impl Tickets {
    pub fn new() -> Self {
        Self {
            tickets: RwLock::new(HashMap::new()),
        }
    }

    pub fn insert(&self, ticket: Ticket) {
        if let Ok(mut tickets) = self.tickets.write() {
            tickets.insert(ticket.id, ticket);
        }
    }

    pub fn update(&self, ticket: Ticket) {
        if let Ok(mut tickets) = self.tickets.write() {
            if let Some(fetched) = tickets.get_mut(&ticket.id) {
                fetched.guild_id = ticket.guild_id;
                fetched.category_id = ticket.category_id;
                fetched.channel_id = ticket.channel_id;
                fetched.contact_name = ticket.contact_name;
            }
        }
    }

    pub fn fetch(&self, id: u64) -> Option<Ticket> {
        if let Ok(tickets) = self.tickets.read() {
            tickets.get(&id).cloned()
        } else {
            None
        }
    }
}

pub static TICKETS: Lazy<Tickets> = Lazy::new(Tickets::new);
