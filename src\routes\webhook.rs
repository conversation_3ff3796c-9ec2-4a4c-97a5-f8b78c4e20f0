use rocket::{State, post, serde::json::<PERSON><PERSON>};
use serde::Deserialize;
use serenity::all::{
    ChannelId, ChannelType, GuildId, PermissionOverwrite, PermissionOverwriteType, Permissions,
    UserId, builder::CreateChannel,
};

use crate::{RocketState, tickets::TICKETS};

#[derive(Debug, Deserialize)]
pub struct WebhookPayload<'a> {
    message_id: &'a str,
    ticket_id: &'a str,
    message: &'a str,
    user_id: &'a str,
    user_name: &'a str,
    user_email: &'a str,
    contact_id: &'a str,
    contact_name: &'a str,
    contact_email: Option<&'a str>,
    contact_identifier: Option<&'a str>,
    channel_id: &'a str,
}

#[post("/webhook", data = "<payload>")]
pub async fn handler<'a>(payload: Json<WebhookPayload<'a>>, state: &State<RocketState>) {
    println!("recv /webhook with payload: {:?}", payload);

    if let Some(contact_identifier) = payload.contact_identifier {
        if contact_identifier.starts_with("custom-") {
            if let Ok(contact_identifier) = contact_identifier.parse::<u64>() {
                let ticket = TICKETS.fetch(contact_identifier);

                if let None = ticket {
                    return;
                }

                let ticket = ticket.unwrap();

                if let Ok(channel) = state
                    .discord_http
                    .get_channel(ChannelId::new(ticket.channel_id))
                    .await
                {
                } else {
                    let guild = state
                        .discord_http
                        .get_guild(GuildId::new(ticket.guild_id))
                        .await;

                    if let Err(_) = guild {
                        return;
                    }

                    let guild = guild.unwrap();

                    // Create permission overwrites based on the constants from TypeScript
                    // TicketChannelEveryonePermissions: ViewChannel: false, but allow other permissions
                    let everyone_permissions = PermissionOverwrite {
                        allow: Permissions::SEND_MESSAGES
                            | Permissions::EMBED_LINKS
                            | Permissions::ATTACH_FILES
                            | Permissions::ADD_REACTIONS
                            | Permissions::USE_EXTERNAL_EMOJIS
                            | Permissions::SEND_VOICE_MESSAGES
                            | Permissions::READ_MESSAGE_HISTORY,
                        deny: Permissions::VIEW_CHANNEL,
                        kind: PermissionOverwriteType::Role(guild.id.everyone_role()),
                    };

                    // TicketChannelUserPermissions: ViewChannel: true
                    let user_permissions = PermissionOverwrite {
                        allow: Permissions::VIEW_CHANNEL,
                        deny: Permissions::empty(),
                        kind: PermissionOverwriteType::Member(UserId::new(ticket.user_id)),
                    };

                    let builder = CreateChannel::new(ticket.contact_identifier)
                        .kind(ChannelType::Text)
                        .category(ChannelId::new(ticket.category_id))
                        .permissions(vec![everyone_permissions, user_permissions]);

                    if let Ok(_channel) = guild.create_channel(&state.discord_http, builder).await {
                    }
                }
            }
        }
    }
}
